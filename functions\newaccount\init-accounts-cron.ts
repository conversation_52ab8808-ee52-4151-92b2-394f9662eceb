import { EventContext } from '@cloudflare/workers-types';
import { Env } from '../types';
import { drizzle } from 'drizzle-orm/d1';
import { newaccountsTable } from '../db/schema';
import { eq, and, lt } from 'drizzle-orm';
import moment from 'moment-timezone';

/**
 * 每天晚上20点执行的初始化账号任务
 * 首先判断今天的日期，然后找到小于今天日期零点的账号且 resetStatus=1 && initStatus==0 的账号
 * 取十条账号，然后将账号发送给初始化请求
 */
export const onRequest = async (context: EventContext<Env, string, Record<string, unknown>>): Promise<Response> => {
    const env = context.env as Env;
    const db = drizzle(env.DB);

    try {
        // 获取当前时间
        const currentTime = moment().tz('Asia/Shanghai').format('YYYY-MM-DD HH:mm:ss');

        console.log(`[Init Accounts Cron] Starting execution at ${currentTime}`);

        // 获取今天零点的时间戳
        const todayMidnight = moment().tz('Asia/Shanghai').startOf('day').format('YYYY-MM-DD HH:mm:ss');
        // 获取昨天零点的时间戳
        const yesterdayMidnight = moment().tz('Asia/Shanghai').subtract(1, 'day').startOf('day').format('YYYY-MM-DD HH:mm:ss');

        let initialDatetime = yesterdayMidnight;

        console.log(`[Init Accounts Cron] Searching for accounts with resetDatetime < ${initialDatetime}`);

        // 查询符合条件的账号：resetStatus=1 && initStatus==0 && resetDatetime < 今天零点
        const accounts = await db.select()
            .from(newaccountsTable)
            .where(and(
                eq(newaccountsTable.resetStatus, 1),
                eq(newaccountsTable.initStatus, 0),
                lt(newaccountsTable.resetDatetime, initialDatetime)
            ))
            .limit(env.SCHEDULE_INIT_ACCOUNT_SIZE || 5);

        if (accounts.length === 0) {
            return new Response(JSON.stringify({
                success: true,
                message: `No accounts found matching criteria (resetStatus=1 && initStatus=0 && resetDatetime < ${initialDatetime})`,
                count: 0,
                timestamp: currentTime,
                searchCriteria: {
                    resetStatus: 1,
                    initStatus: 0,
                    resetDatetimeBefore: initialDatetime
                }
            }), {
                status: 200,
                headers: { 'Content-Type': 'application/json' }
            });
        }

        // 发送请求到初始化账号API (不等待响应)
        fetch('https://89dbbd46cf2039858a0f44184c36603f62447fe4-7860.dstack-prod8.phala.network/init-account', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'User-Agent': 'Seedlog-Cron/1.0',
                'Authorization': `Bearer ${env.API_RESET_TOKEN}`
            },
            body: JSON.stringify(accounts)
        }).catch(error => {
            console.error('[Init Accounts Cron] Failed to send request:', error);
        });
        await new Promise((resolve) => setTimeout(resolve, 5000));
        // 记录执行结果
        const logMessage = `Successfully sent ${accounts.length} accounts to init API (fire-and-forget)`;

        console.log(`[Init Accounts Cron] ${logMessage}`, {
            timestamp: currentTime,
            accountCount: accounts.length,
            accountEmails: accounts.map(a => a.email),
            accountResetDates: accounts.map(a => ({ email: a.email, resetDatetime: a.resetDatetime })),
            searchCriteria: {
                resetStatus: 1,
                initStatus: 0,
                resetDatetimeBefore: initialDatetime
            }
        });

        return new Response(JSON.stringify({
            success: true,
            message: logMessage,
            accountCount: accounts.length,
            accountEmails: accounts.map(a => a.email),
            accountResetDates: accounts.map(a => ({ email: a.email, resetDatetime: a.resetDatetime })),
            searchCriteria: {
                resetStatus: 1,
                initStatus: 0,
                resetDatetimeBefore: initialDatetime
            },
            timestamp: currentTime
        }), {
            status: 200,
            headers: { 'Content-Type': 'application/json' }
        });

    } catch (error) {
        const errorMessage = error instanceof Error ? error.message : 'Unknown error occurred';
        const currentTime = moment().tz('Asia/Shanghai').format('YYYY-MM-DD HH:mm:ss');

        console.error('[Init Accounts Cron] Error:', error);

        return new Response(JSON.stringify({
            success: false,
            error: errorMessage,
            timestamp: currentTime
        }), {
            status: 500,
            headers: { 'Content-Type': 'application/json' }
        });
    }
};
